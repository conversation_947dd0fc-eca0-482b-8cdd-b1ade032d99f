"use client";

import { ProductPagination, usePagination } from "./ProductPagination";

// Example usage component
export function ProductPaginationDemo() {
  // Example data
  const totalItems = 250; // Total number of items
  const pageSize = 10; // Items per page

  // Using the custom hook for additional functionality
  const {
    currentPage,
    totalPages,
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToPreviousPage,
    goToNextPage,
  } = usePagination(pageSize, totalItems);

  return (
    <div className="space-y-6 p-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Pagination Demo</h2>
        <p className="text-gray-600 mb-2">
          Showing page {currentPage} of {totalPages} ({totalItems} total items)
        </p>
        <p className="text-sm text-gray-500">
          Items {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalItems)} of {totalItems}
        </p>
      </div>

      {/* Main pagination component */}
      <ProductPagination
        pageSize={pageSize}
        totalItems={totalItems}
        className="justify-center"
      />

      {/* Additional controls using the hook */}
      <div className="flex justify-center gap-2 mt-4">
        <button
          onClick={goToFirstPage}
          disabled={currentPage === 1}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          First
        </button>
        <button
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
        <button
          onClick={goToLastPage}
          disabled={currentPage === totalPages}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Last
        </button>
      </div>

      {/* Direct page input */}
      <div className="flex justify-center items-center gap-2">
        <label htmlFor="pageInput" className="text-sm">
          Go to page:
        </label>
        <input
          id="pageInput"
          type="number"
          min="1"
          max={totalPages}
          defaultValue={currentPage}
          className="w-16 px-2 py-1 text-sm border rounded"
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              const page = parseInt((e.target as HTMLInputElement).value);
              if (page >= 1 && page <= totalPages) {
                goToPage(page);
              }
            }
          }}
        />
        <span className="text-sm text-gray-500">of {totalPages}</span>
      </div>
    </div>
  );
}
