"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "../ui/button";

// Types for the component
interface FilterState {
  searchQuery: string;
  myProducts: boolean;
  sortBy: string;
}

// Sort options as specified
const SORT_OPTIONS = [
  { value: "name-asc", label: "<PERSON>zwa (A-Z)" },
  { value: "name-desc", label: "<PERSON><PERSON><PERSON> (Z-A)" },
  { value: "calories-asc", label: "<PERSON>lorie (0-9)" },
  { value: "calories-desc", label: "Kalorie (9-0)" },
] as const;

interface ProductFilterProps {
  onFilterChange?: (filters: FilterState) => void;
  onSearch?: (query: string) => void;
  onMyProductsToggle?: (enabled: boolean) => void;
  onSortChange?: (sortBy: string) => void;
  className?: string;
}

export function ProductFilter({
  onFilterChange,
  onSearch,
  onMyProductsToggle,
  onSortChange,
  className,
}: ProductFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize filters with URL params
  const [filters, setFilters] = React.useState<FilterState>({
    searchQuery: searchParams.get("searchQuery") || "",
    myProducts: searchParams.get("myProducts") === "true",
    sortBy: searchParams.get("sort") || "name-asc",
  });

  // Update filters when URL params change
  React.useEffect(() => {
    const sortFromUrl = searchParams.get("sort");
    if (sortFromUrl && sortFromUrl !== filters.sortBy) {
      setFilters((prev) => ({ ...prev, sortBy: sortFromUrl }));
    }
  }, [searchParams, filters.sortBy]);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    const newFilters = { ...filters, searchQuery: query };
    setFilters(newFilters);
  };

  // Handle my products checkbox toggle
  const handleMyProductsChange = (checked: boolean) => {
    const newFilters = { ...filters, myProducts: checked };
    setFilters(newFilters);
  };

  // Handle sort selection change
  const handleSortChange = (value: string) => {
    const newFilters = { ...filters, sortBy: value };
    setFilters(newFilters);

    // Update sort URL parameter
    const params = new URLSearchParams(searchParams.toString());
    params.set("sort", value);
    router.push(`?${params.toString()}`);
  };

  const handleSearch = () => {
    const param = new URLSearchParams(searchParams.toString());
    // Update URL search parameter
    if (filters.searchQuery === "") {
      param.delete("searchQuery");
    } else {
      param.set("searchQuery", filters.searchQuery);
    }
    router.push(`?${param.toString()}`);

    // Update URL myProducts parameter
    if (filters.myProducts === false) {
      param.delete("myProducts");
    } else {
      param.set("myProducts", filters.myProducts.toString());
    }
    router.push(`?${param.toString()}`);
  };

  return (
    <div
      className={`flex flex-col gap-4 p-4 bg-background ${
        className || ""
      }`}
    >
      {/* Search Input */}
      <div className="flex flex-col gap-4 flex-1 lg:flex-row">
        <Input
          type="text"
          placeholder="Szukaj produktu po nazwie..."
          value={filters.searchQuery}
          onChange={handleSearchChange}
        />
        <Button onClick={handleSearch} className="w-full lg:w-[200px]">
          Szukaj
        </Button>
      </div>

      <div className="flex flex-col gap-4 lg:justify-between lg:flex-row">
        {/* My Products Checkbox */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="my-products"
            checked={filters.myProducts}
            onCheckedChange={handleMyProductsChange}
          />
          <label
            htmlFor="my-products"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Moje produkty
          </label>
        </div>

        {/* Sort Dropdown */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium whitespace-nowrap">
            Sortowanie
          </span>
          <Select value={filters.sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-full lg:w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

export type { FilterState, ProductFilterProps };
